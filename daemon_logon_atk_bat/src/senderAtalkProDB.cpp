#include "senderMMSProcessDB.h"
#include "Encrypt.h"
#include "ksbase64.h"
#include <sys/time.h>
//#include <time.h>
static CMMSPacketSend mmsPacketSend;
static int S_PROCESS_NO;

// 20170621 MMSID SEQ USE
static int proc_id;

int main(int argc,char* argv[])
{
	/*
	 * 1 : sockfd
	 * 2 : pipe
	 * 3 : version
	 * 4 : conf file
	 */
	int sockfd;
	int fd;
	int ret;
	char buff[SOCKET_BUFF];
	CLogonDbInfo logonDbInfo;
	
	sockfd = atoi(argv[1]);
	fd = atoi(argv[2]);
	memset(&logonDbInfo,0x00,sizeof(logonDbInfo));
	read(fd,(char*)&logonDbInfo,sizeof(logonDbInfo));
	close(fd);
	
	memset(_DATALOG,0x00,sizeof(_DATALOG));//CCL(_DATALOG);
	memset(_MONILOG,0x00,sizeof(_MONILOG));//CCL(_MONILOG);
	char* p;
	
	sprintf(_DATALOG,"%s/",logonDbInfo.szLogPath);
	sprintf(_MONILOG,"%s/",logonDbInfo.szLogPath);
	
	memset(szSenderID,0x00,sizeof(szSenderID));//CCL(szSenderID);
	strcpy(szSenderID,logonDbInfo.szCID);
	S_PROCESS_NO = getpid();

	p = strtok(logonDbInfo.szLogFilePath,"|");
	if( p )
	{
		strcat(_MONILOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get monitor [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	p = strtok(NULL,"|");
	if( p )
	{
		strcat(_DATALOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get data [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	logPrintS(0,"[INF] filepath - logfile[%s] monitorfile[%s] PID[%d]",_DATALOG,_MONILOG, S_PROCESS_NO);
	logPrintS(0,"[INF] argv[4][%s]",argv[4]);
		
	ret = configParse(argv[4]);
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] configParse Failed");
	    exit(1);
	}
	
	logPrintS(0,"[INF] config file - logonDBName [%s]",gConf.logonDBName);
	
	if (g_oracle.connectToOracle(gConf.dbuid, gConf.dbdsn)<0)
	{
		logPrintS(0,"[ERR] connectToOracle Failed");
	    return -1;
	}
	
	logPrintS(1,"[INF] ORACLE CONNECT");
	
	// 20170621 MMSID SEQ USE
	proc_id = g_oracle.selectSEQ();
	
	if( proc_id == -1 )
		proc_id = 9999;
		
	SenderProcess *mSenderProcess = new SenderProcess();

	// sendre Main Process Start 
	mSenderProcess->SenderMain(sockfd,logonDbInfo);
	
	if (g_oracle.closeFromOracle()<0)
	{
		logPrintS(0,"[ERR] closeFromOracle Failed");
	    return -1;
	}
	logPrintS(1,"[INF] ORACLE DISCONNECT");
	
	return 0;
}


int sendAck(CKSSocket& hRemoteSock,CMMSPacketSend& mmsPacketSend, int nCode,int ctnid,string strDesc)
{
	int ret=0;
	string strPacket;
	strPacket = "";
	strPacket.reserve(0);
	string key;
	char szCode[8];

	memset(szCode		,0x00	,sizeof(szCode));

	sprintf(szCode,"%d",nCode);

	key = mmsPacketSend.getKeyValue();

	strPacket = "BEGIN ACK\r\nKEY:" + key + "\r\nCODE:" ;
	strPacket += szCode;
	strPacket += "\r\nDESC:" + strDesc + "\r\nEND\r\n";
	
	//logPrintS(1,"[INF] send ack packet[%s]", strPacket.c_str() );
		
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	logPrintS(1,"[INF] send ack ctnid[%d]key[%s]code[%s]strDesc[%s]", ctnid, key.c_str(), szCode, strDesc.c_str() );

	return ret;
}

int sendPong(CKSSocket& hRemoteSock)
{
	string strPacket;
	string strKey;
	CMMSPacketBase packetBase;
	int ret;
	
	packetBase.findValue((char*)hRemoteSock.getMsg(),"KEY",strKey);

	strPacket = "BEGIN PONG\r\nKEY:" + strKey + "\r\nEND\r\n";
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	
	if( ret != strPacket.length() )
	{
		logPrintS(0,"[ERR] socket ack send failed sendSize/packetSize[%d/%d]",ret,strPacket.length());
		return ret;
	}
	//logPrintS(0,"[INF] socket link PONG send");
  
	fflush(stdout);
	return 0;
}

/** @return 음수 일시 프로세스 종료 */
int recvLink(CKSSocket& hRemoteSock,char* buff)
{
	int ret;

	TypeMsgDataAck* pLinkAck = (TypeMsgDataAck*)buff;
	memset(pLinkAck->header.msgType, 0x00, sizeof(pLinkAck->header.msgType));
	strcpy(pLinkAck->header.msgType,"8");

	ret = hRemoteSock.send(buff,sizeof(TypeMsgDataAck));
	if( ret != sizeof(TypeMsgDataAck))
	{
		logPrintS(0,"[ERR] socket link ack send failed - errno[%s] sendSize/packetSize[%d/%d]", strerror(errno), ret, sizeof(TypeMsgDataAck));
		return -1;
	}
	time(&SLastTLink);

	return 0;
}

void logPrintS(int type, const char *format, ...)
{
	va_list args;
	char logMsg[SOCKET_BUFF];
	char tmpMsg[SOCKET_BUFF];

	va_start(args, format);
	vsprintf(tmpMsg, format, args);
	va_end(args);

	sprintf(logMsg,"[S][%s] %s",szSenderID,tmpMsg);
	if (type==1)
	{
		_logPrint(_DATALOG,logMsg);
	}
	else
	{
		_monPrint(_MONILOG,logMsg);
	}
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;

	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
	if( gConf.logonDBName == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	
	conf.strncpy2(gConf.monitorName , conf.FetchEntry("domain.monitor"),64);
	
	if( gConf.monitorName == NULL )
	{
		strcpy(gConf.monitorName,"");
	}

	conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.self"),64);
	if( gConf.domainPath == NULL )
	{
		strcpy(gConf.domainPath,"");
	}

	conf.strncpy2(gConf.ContentPath , conf.FetchEntry("path.mmscontent"),64);
	
	if( gConf.ContentPath == NULL )
	{
		strcpy(gConf.ContentPath,"");
	}

	gConf.socketLinkTimeOut = conf.FetchEntryInt("socket.linktimeout");
	
	if( gConf.socketLinkTimeOut <= 1 )
	{
		gConf.socketLinkTimeOut = 2;
	}

	gConf.dbRequestTimeOut = conf.FetchEntryInt("db.requesttimeout");

	if( gConf.dbRequestTimeOut <= 0 )
	{
		gConf.dbRequestTimeOut = 1;
	}
	
	conf.strncpy2(gConf.dbuid , conf.FetchEntry("db.uid"),64);
	
	if( gConf.dbuid == NULL )
	{
		strcpy(gConf.dbuid,"");
	}

	conf.strncpy2(gConf.dbdsn , conf.FetchEntry("db.dsn"),64);
	
	if( gConf.dbdsn == NULL )
	{
		strcpy(gConf.dbuid,"");
	}
	
	gConf.dbMmsIdHeader = conf.FetchEntryInt("db.mmsidhead");

	if( gConf.dbMmsIdHeader <= 0 )
	{
		gConf.dbMmsIdHeader = 0;
	}
	
	return 0;
}


void viewPackSender(char *a,int n)
{
	int i;
	char logMsg[VIEWPACK_MAX_SIZE];
	char strtmp[VIEWPACK_MAX_SIZE];
	
	memset(logMsg,0x00, sizeof logMsg);
	memset(strtmp,0x00, sizeof strtmp);
	
	for(i=0;i<n;i++)
	{
		if( a[i] == 0x00 )
		{
			strtmp[i] = '.';
	    }
	    else
		{
			memcpy(strtmp+i,a+i,1);
		}
	}
	
	sprintf(logMsg,"info:[%s]",strtmp);
	_monPrint(_MONILOG,logMsg);
	
	return ;
}

int setMMSMSG2DB_ATK(long long nMMSId,CMMSPacketSend& mmsPacketSend, int priority)
{
	int ret, size;
    char szType[50+1];
    char* pData;
    CMData mData;
	CSenderDbMMSMSG_TALK senderDbMMSMSG;
	
	//pData = (char*)malloc(sizeof(pData)*sizeof(char));
	
    ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
	{
        logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}
	
	memset(szType, 0x00, sizeof(szType));
    sprintf(szType,(char*)mData.contentType.strType.c_str());
    if (strcmp(szType, "TXT") == 0)
    {
    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
    }
    else
    {
    	while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
	    {
	        memset(szType,0x00,sizeof(szType));	
	        sprintf(szType,(char*)mData.contentType.strType.c_str());
		    if (strcmp(szType, "TXT") == 0)
		    {
		    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
		    	break;
		    }
	    }
    }
        
	memset(&senderDbMMSMSG,0x00,sizeof(CSenderDbMMSMSG_TALK));
	//암호화 체크
	if(strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), " aes_base64", 10) == 0) 
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));
		if(strlen((char*)receiverNum)> 0)
		{
			sprintf(senderDbMMSMSG.szDstAddr ,"82%s", string((char*)receiverNum).substr(1).c_str());
			////logPrintS(1,"[INFO] [%s]", senderDbMMSMSG.szDstAddr);
		}else{
			sprintf(senderDbMMSMSG.szDstAddr,"82%s", "");
		}
		//메시지 암호화 풀기	
		//logPrintS(1,"[INFO] pData[%d]", strlen((char*)pData));
		en.decrypt((unsigned char*)pData, (unsigned char*)pData, atoi(mmsPacketSend.getMsgOrgSizeValue()));
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), "%s", (char*)pData);
		strncpy(senderDbMMSMSG.szMsgBody, (char*)pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
		//logPrintS(1,"[INFO] szMsgBody[%s]", senderDbMMSMSG.szMsgBody);
		free(receiverNum);
	}
	else
	{
		//sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
		snprintf(senderDbMMSMSG.szDstAddr,sizeof(senderDbMMSMSG.szDstAddr)-1 ,"82%s", mmsPacketSend.getReceiverValue()+1);
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), pData);
		strncpy(senderDbMMSMSG.szMsgBody, pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
	}
    
    //cout<<"pData : "<<pData<<"\n"<<endl;
    //cout<<"msgbody : "<<senderDbMMSMSG.szMsgBody<<"\n"<<endl;
    
	//sprintf(senderDbMMSMSG.szQName,"%d", getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, 0));
	sprintf(senderDbMMSMSG.szQName,"%d", 
			getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, gSenderInfo.szQType));
			
	//strcpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue());
	strncpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue(),sizeof(senderDbMMSMSG.szSenderKey)-1);
	//strcpy(senderDbMMSMSG.szDstAddr   ,mmsPacketSend.getReceiverValue());
	//sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
	
	snprintf(senderDbMMSMSG.szTmplCd, sizeof(senderDbMMSMSG.szTmplCd), mmsPacketSend.getTmplCdValue());
	
	//배포Agent 중 btname, bturl 이 string 으로 "null" 로 오는 것이 있어서 예외 처리
	if(strncmp(mmsPacketSend.getBtNameValue(), "null", 4) == 0)
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), "");
		snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), "");
	}
	else
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), mmsPacketSend.getBtNameValue());
		//snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), mmsPacketSend.getBtUrlValue());
		strncpy(senderDbMMSMSG.szBtUrl,  mmsPacketSend.getBtUrlValue(),sizeof(senderDbMMSMSG.szBtUrl)-1);
	}
	
	//snprintf(senderDbMMSMSG.szButton, sizeof(senderDbMMSMSG.szButton), mmsPacketSend.getButtonValue());
	strncpy(senderDbMMSMSG.szButton,  mmsPacketSend.getButtonValue(),sizeof(senderDbMMSMSG.szButton)-1);
	
	//snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), mmsPacketSend.getBtNameValue());
	//snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), mmsPacketSend.getBtUrlValue());

	if(strlen(mmsPacketSend.getAttachmentValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szAttachment, mmsPacketSend.getAttachmentValue(),sizeof(senderDbMMSMSG.szAttachment)-1);
	}
	else 
	{
		//snprintf(senderDbMMSMSG.szButton, sizeof(senderDbMMSMSG.szButton), mmsPacketSend.getButtonValue());
		strncpy(senderDbMMSMSG.szButton,  mmsPacketSend.getButtonValue(),sizeof(senderDbMMSMSG.szButton)-1);
	}
	
	if(strlen(mmsPacketSend.getSupplementValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szSupplement, mmsPacketSend.getSupplementValue(),sizeof(senderDbMMSMSG.szSupplement)-1);
	}
	
	cout<<"mmsPacketSend getSupplementValue["<<mmsPacketSend.getSupplementValue()<<"]\n"<<endl;
	cout<<"senderDbMMSMSG szSupplement"<<senderDbMMSMSG.szSupplement<<"\n"<<endl;
	
	if(strlen(mmsPacketSend.getKkoHeaderValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szKkoHeader, mmsPacketSend.getKkoHeaderValue(),sizeof(senderDbMMSMSG.szKkoHeader)-1);
	}
	
	if(strlen(mmsPacketSend.getMessageTypeValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szMessageType, mmsPacketSend.getMessageTypeValue(),sizeof(senderDbMMSMSG.szMessageType)-1);
	}
	
	//snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), mmsPacketSend.getBtNameValue());
	//snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), mmsPacketSend.getBtUrlValue());
	
	if(strlen(mmsPacketSend.getMethodValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szResMethod,  mmsPacketSend.getMethodValue(),sizeof(senderDbMMSMSG.szResMethod)-1);
	}else{
		snprintf(senderDbMMSMSG.szResMethod, sizeof(senderDbMMSMSG.szResMethod), gSenderInfo.szResMethod);
	}
	
	if(strlen(mmsPacketSend.getTimeoutValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szTimeout,  mmsPacketSend.getTimeoutValue(),sizeof(senderDbMMSMSG.szTimeout)-1);
	}else{
		snprintf(senderDbMMSMSG.szTimeout, sizeof(senderDbMMSMSG.szTimeout), gSenderInfo.szTimeout);
	}
	
	strncpy(senderDbMMSMSG.szTitle, mmsPacketSend.getTitleValue(),sizeof(senderDbMMSMSG.szTitle)-1);

	senderDbMMSMSG.nMMSId = nMMSId;
	//20180829 우선순위 적용
	senderDbMMSMSG.nPriority = priority;
	
	strncpy(senderDbMMSMSG.szPrice, mmsPacketSend.getPriceValue(),sizeof(senderDbMMSMSG.szPrice)-1);
	strncpy(senderDbMMSMSG.szCurType, mmsPacketSend.getCurTypeValue(),sizeof(senderDbMMSMSG.szCurType)-1);	
    
    free(pData);
	//ret = g_oracle.setMMSMSG_ATK_V2(senderDbMMSMSG);
	ret = g_oracle.setMMSMSG_ATK_V4(senderDbMMSMSG);
	
	if (ret <= 0)
	{
		logPrintS(0,"[ERR] setMMSMSG_ATK_V4 MMSID[%lld] ret[%d]", nMMSId, ret);
		return ret;
	}
	else
	{
		ret = 100;	
		return ret;
	}		
	
}

int setMMSTBL2DB(long long nMMSId, int ctnid,int priority,CBcastData * pBcastData)
{
	int ret;
	CSenderDbMMSTBL senderDbMMSTBL;
	CMData mData;
	char szType[50+1];
	char *pData;
	int textSize=0;

	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}

	memset(&senderDbMMSTBL,0x00,sizeof(CSenderDbMMSTBL));
	
	memset(szType, 0x00, sizeof(szType));
	sprintf(szType,(char*)mData.contentType.strType.c_str());
	
	if (strcmp(szType, "REPTXT") == 0)
	{
		pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &textSize);
	}
	else
	{
		while((ret = mmsPacketSend.getMDataNext(mData)) == 0 )
		{
			memset(szType,0x00,sizeof(szType));	
			sprintf(szType,(char*)mData.contentType.strType.c_str());
			if (strcmp(szType, "REPTXT") == 0)
			{
				pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &textSize);
				break;
			}
		}
	}

	if(strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), " aes_base64", 10) == 0) 
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(),
			strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		//logPrintS(1,"receiverNum:%d", atoi(mmsPacketSend.getPhoneSizeValue()));
		//en.decrypt(receiverNum, receiverNum, atoi(mmsPacketSend.getPhoneSizeValue()));		
		en.decrypt(receiverNum, receiverNum, size);
		//strcpy(senderDbMMSTBL.szDstAddr, (char*)receiverNum);
		snprintf(senderDbMMSTBL.szDstAddr, sizeof(senderDbMMSTBL.szDstAddr), "%.*s", size,(char*)receiverNum);
		free(receiverNum);
		
		//메시지 암호화 풀기	
		//logPrintS(1,"[INFO] pData[%d]", strlen((char*)pData));
		en.decrypt((unsigned char*)pData, (unsigned char*)pData, atoi(mmsPacketSend.getMsgOrgSizeValue()));
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), "%s", (char*)pData);
		strncpy(senderDbMMSMSG.szMsgBody, (char*)pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
		//logPrintS(1,"[INFO] szMsgBody[%s]", senderDbMMSMSG.szMsgBody);
		free(receiverNum);
	}
	else
	{
		//sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
		snprintf(senderDbMMSMSG.szDstAddr,sizeof(senderDbMMSMSG.szDstAddr)-1 ,"82%s", mmsPacketSend.getReceiverValue()+1);
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), pData);
		strncpy(senderDbMMSMSG.szMsgBody, pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
	}

	


	if (strlen(mmsPacketSend.getSenderValue()) <= 0)
		strcpy(senderDbMMSTBL.szCallBack, "0");
	else
		strcpy(senderDbMMSTBL.szCallBack, mmsPacketSend.getSenderValue());

	if(strncmp(mData.strEncoding.c_str(), " aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0)
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		//logPrintS(1,"receiverNum:%d", atoi(mmsPacketSend.getPhoneSizeValue()));
		en.decrypt(receiverNum, receiverNum, atoi(mmsPacketSend.getPhoneSizeValue()));
		strcpy(senderDbMMSTBL.szDstAddr, (char*)receiverNum);
		free(receiverNum);
	}
	else
	{
		strcpy(senderDbMMSTBL.szDstAddr , mmsPacketSend.getReceiverValue());
	}

	strcpy(senderDbMMSTBL.szMsgTitle, mmsPacketSend.getSubjectValue() );
	strcpy(senderDbMMSTBL.szPtnSn   , mmsPacketSend.getKeyValue()     );
	strcpy(senderDbMMSTBL.szResvData, mmsPacketSend.getExtendValue()  );
	strcpy(senderDbMMSTBL.szCid     , szSenderID                      );
	senderDbMMSTBL.nMsgType 	= 1;		/* 0: LMS|MMS, 1: Alim Talk */
	senderDbMMSTBL.nPriority 	= priority;
	senderDbMMSTBL.nCtnId 		= ctnid;
	senderDbMMSTBL.nCtnType 	= mmsPacketSend.getCtnType();
	senderDbMMSTBL.nRgnRate 	= 65;
	senderDbMMSTBL.nInterval 	= 10;
	senderDbMMSTBL.nTextCnt 	= mmsPacketSend.getTextCnt();
	senderDbMMSTBL.nImgCnt 		= mmsPacketSend.getImgCnt();
	senderDbMMSTBL.nAugCnt 		= mmsPacketSend.getAugCnt();
	senderDbMMSTBL.nMpCnt 		= mmsPacketSend.getMpCnt();
	senderDbMMSTBL.nMMSId 		= nMMSId;
	
	//20190708 senderkey add
	strncpy(senderDbMMSTBL.szSenderKey ,mmsPacketSend.getSenderKeyValue(),sizeof(senderDbMMSTBL.szSenderKey)-1);

    ret = g_oracle.setMMSTBL(senderDbMMSTBL);
	
	if (ret <= 0)
	{
		logPrintS(0,"[ERR] setMMSTBL MMSID[%lld] ret[%d]",nMMSId, ret);
	}
	
	return ret;
}


/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getCTNID2DB(CSenderDbMMSID& senderDbMMSID)
{
	senderDbMMSID.ctnid = -1;
	return 0;
}

/**====================================================================================**/
// 2014020 make mmsid
// 20170621 MMSID seq use
/**====================================================================================**/
long long mkMMSID(int mmsIdHeader)
{

	char	pch[30];
	char	pchlast[30];
	
	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;
	
	long long ulltm;
	
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);

	memset(pch				,0x00		,sizeof(pch));	
	sprintf(pch, "%2d%02d%02d%02d%09d"
				,tp.tm_mday+10
				,tp.tm_hour
				,tp.tm_min
				,tp.tm_sec
				,(int)tmv.tv_nsec
				);

		
	memset(pchlast		,0x00		,sizeof(pchlast));
	sprintf(pchlast,"%.12s%04d", pch, proc_id);
	
	ulltm = atoll(pchlast);

	//logPrintS(1,"[INF] mkmmsid pch[%s] pchlast[%s] ulltmid[%lld]", pch, pchlast, ulltm );

	return ulltm;
	
}

/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getMMSID2DB(CSenderDbMMSID& senderDbMMSID, char* cid)
{
	int ret = 0;
	senderDbMMSID.mmsid	= 0;
	memcpy(senderDbMMSID.szCid, cid, 10);
	senderDbMMSID.mmsid = g_oracle.getMMSID();

	/**====================================================================================**/
	// 20140212 : MOD if div
	/**====================================================================================**/
	//if(senderDbInfoAck.mmsid == 0 || senderDbInfoAck.mmsid < 0)
	if(senderDbMMSID.mmsid == 0 || senderDbMMSID.mmsid < 0)
	{
		logPrintS(0,"[ERR] socket_domain get mms id to db failed - mmsid[%lld]", senderDbMMSID.mmsid);
		// 2013.12 LSY 보완 필요
		//senderDbMMSID.mmsid = 999999999;
		senderDbMMSID.mmsid = mkMMSID(gConf.dbMmsIdHeader);
				
		//return 0;	// 20140212 : ADD return
		if(senderDbMMSID.mmsid == 0 || senderDbMMSID.mmsid < 0)
		return -1;
	}

	if(senderDbMMSID.mmsid < 0)
	{
		return -1;
	}

	return 1;
	/**====================================================================================**/
}

int getTelcoId(int imgCnt, char* szTelco, int nColorYN)
{
	char* p;
	int telcoArray[4];
	int i = 0;
	
	memset(telcoArray,0x00,sizeof(telcoArray));

	p = strtok(szTelco,"|");
	
	if( p == NULL )
	{
		return 1;
	}

	telcoArray[i++] = atoi(p);

	while(p = strtok(NULL,"|") )
	{
		telcoArray[i++]= atoi(p);
		if( i >= 4 )
		{
			break;
		}
	}

	//szTelco 다시 복구.버그 수정.2012.12.10.
	sprintf(szTelco, "%d|%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3]);
	return telcoArray[3];
	
}

int getTelcoId(int imgCnt, char* szTelco, char *szQType)
{
	char* p;
	int telcoArray[6];
	int i = 0;
		
	memset(telcoArray,0x00,sizeof(telcoArray));

	p = strtok(szTelco,"|");
	
	if( p == NULL )
	{
		return 1;
	}
	telcoArray[i++] = atoi(p);

	while(p = strtok(NULL,"|") )
	{
		telcoArray[i++]= atoi(p);
		if( i >= 6 )
		{
			break;
		}
	}
	
	sprintf(szTelco, "%d|%d|%d|%d|%d|%d", 
			telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3], telcoArray[4], telcoArray[5]);

	//실시간큐
	if(strncmp(szQType, "type_real", 9) == 0) 
	{
		return telcoArray[3];
	}
	//배치큐
	else if(strncmp(szQType, "type_bt", 7) == 0)
	{
		return telcoArray[5];
	}
	else
	{
		return telcoArray[3];
	}
}


void writeLogMMSData(CMMSPacketSend& mmsPacketSend,long long mmsid, int ctnid)
{
    logPrintS(1,"[INF] send message mmsid[%lld]ctnid[%d]key[%s]extend[%s]subject[%s]dst[]call[%s]",
            mmsid,
            ctnid,
            mmsPacketSend.getKeyValue(),
            mmsPacketSend.getExtendValue(),
            mmsPacketSend.getSubjectValue(),
         /*   mmsPacketSend.getReceiverValue(),*/
            mmsPacketSend.getSenderValue()
            /*mmsPacketSend.getContentCntValue(),
            mmsPacketSend.getPsktynValue(),
            mmsPacketSend.getPktfynValue(),
            mmsPacketSend.getPlgtynValue(),
            mmsPacketSend.getTextCntValue(),
            mmsPacketSend.getImgCntValue(),
            //mmsPacketSend.getAudCntValue(),
            mmsPacketSend.getMpCntValue(),
            mmsPacketSend.getBcastCntValue(),
            mmsPacketSend.getMsgOrgSizeValue()*/
            );
}


SenderProcess::SenderProcess()
{
	bDayWarnCheck=false;
	bMonWarnCheck=false;
}

void SenderProcess::SenderMain(int sockfd,CLogonDbInfo& logonDbInfo)
{
	int ret;
	CLogonUtil util;
	CAdminUtil admin;
	CKSSocket db;
	CProcessInfo processInfo;
	CMonitor monitor;

	memset(&processInfo, 0x00, sizeof(processInfo));
	memset(&gSenderInfo, 0x00, sizeof(gSenderInfo));
	  
	strcpy(processInfo.processName,logonDbInfo.szSenderName);
	get_timestring("%04d%02d%02d%02d%02d%02d",time(NULL),processInfo.startTime);
	
	sprintf(processInfo.szPid,"%d",getpid());
	strcpy(processInfo.logonDBName,gConf.logonDBName);
	
	util.findValueParse(logonDbInfo.szReserve, "mms_tel", gSenderInfo.szSmsTelcoInfo);
	util.findValueParse(logonDbInfo.szReserve, "mms_yn",  gSenderInfo.szSmsFlag);
	util.findValueParse(logonDbInfo.szReserve, "res_method",  gSenderInfo.szResMethod);
	util.findValueParse(logonDbInfo.szReserve, "timeout",  gSenderInfo.szTimeout);
	util.findValueParse(logonDbInfo.szReserve, "q_type",  gSenderInfo.szQType);
	util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);
	
	/*
	 * alim talk flag 추가
	 * sender_key  : 회원사 키
	 * partner_key : 허브 파트너 키(KSKYB)
	 */
	util.findValueParse(logonDbInfo.szReserve, "sender_key",  gSenderInfo.szSenderKey);
	
	strcpy(gSenderInfo.szUrlTelcoInfo,"0");
	strcpy(gSenderInfo.szUrlFlag,"0");
	
	logPrintS(0,"[INF] send process sender main start sockfd[%d]CID[%s]processInfo.startTime[%s]pid[%s]logonDbInfo.Reserve[%s]senderInfo[%s]smsFlag[%s]SenderKey[%s]BlockYN[%s]"
            ,sockfd
            ,logonDbInfo.szCID
            ,processInfo.startTime
            ,processInfo.szPid
            ,logonDbInfo.szReserve
            ,gSenderInfo.szSmsTelcoInfo
            ,gSenderInfo.szSmsFlag
			,gSenderInfo.szSenderKey
			,gSenderInfo.szBlockYN
			);
	
	util.displayLogonDbInfo(logonDbInfo,_MONILOG);

	CKSSocket hRemoteSock;

	int recvLen;
   
	hRemoteSock.attach(sockfd);
   
	ret = admin.createDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] socket_domain create failed - CID[%s]classify[%c]domain_path[%s]", logonDbInfo.szCID, logonDbInfo.classify, gConf.domainPath);
        goto SenderEND;
	}
	
	monitor.Init("logon7", "sender", processInfo.processName, logonDbInfo.szCID, logonDbInfo.nmPID, logonDbInfo.szIP);
	time(&SLastTLink);
	
	nCurAccCnt = 0;

	memset(szLimitTime, 0x00, sizeof(szLimitTime));
	get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitTime);	// 현재 날짜 구하기
	memset(szLimitCurTime, 0x00, sizeof(szLimitCurTime));
    
	while(bSActive)
	{
		//logPrintS(1,"[STP] -0- []");
				
		// 시간측정로그 : 메시지 수신전 시간
		/*				
	 	struct tm *d;
		struct timeval val;
		gettimeofday(&val, NULL);
		d=localtime(&val.tv_sec);		
		logPrintS(1,"0[Msg Recv Before]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
		*/					
		ret = hRemoteSock.recvAllMsg(3);
		//ret = util.recvPacket(hRemoteSock,buff,0,10000);
		if( ret < 0)
		{
			logPrintS(0,"[ERR] socket read msg failed - [%s][%s]",hRemoteSock.getErrMsg(), strerror(errno));
			goto SenderEND;
		}
		      
		if( ret == 0 )
		{
			//wait_a_moment(logonDbInfo.nmCNT);
			ret = admin.checkPacket(processInfo,logonDbInfo,sum);// check admin packet
			if( ret < 0 )
			{
				logPrintS(0,"[ERR] socket_domain packet check failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
				goto SenderEND;
			}
			//logPrintS(1,"[STP] -1- []");
	
			switch(ret) 
			{
				case 3: // end
					bSActive = false;
					
					continue;
				case 5: // info
					memset(gSenderInfo.szSmsTelcoInfo, 0x00, sizeof(gSenderInfo.szSmsTelcoInfo)); 
					memset(gSenderInfo.szSmsFlag, 0x00, sizeof(gSenderInfo.szSmsFlag)); 
					memset(gSenderInfo.szResMethod, 0x00, sizeof(gSenderInfo.szResMethod));
					memset(gSenderInfo.szTimeout, 0x00, sizeof(gSenderInfo.szTimeout));
					memset(gSenderInfo.szQType, 0x00, sizeof(gSenderInfo.szQType));
					memset(gSenderInfo.szBlockYN, 0x00, sizeof(gSenderInfo.szBlockYN));
					util.findValueParse(logonDbInfo.szReserve, "mms_tel" ,gSenderInfo.szSmsTelcoInfo); 
					util.findValueParse(logonDbInfo.szReserve, "mms_yn"	,gSenderInfo.szSmsFlag);
					util.findValueParse(logonDbInfo.szReserve, "res_method",  gSenderInfo.szResMethod);
					util.findValueParse(logonDbInfo.szReserve, "timeout",  gSenderInfo.szTimeout);
					util.findValueParse(logonDbInfo.szReserve, "q_type",  gSenderInfo.szQType);
					util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);
					nCurAccCnt = 0;
					logPrintS(0,"[INF] gSenderInfo.szResMethod[%s]",gSenderInfo.szResMethod);
					logPrintS(0,"[INF] info modify ok");
					break;
				default:
					break;
			}
	
			time(&SThisT);	
			ret = (int)difftime(SThisT,monLastT);
			if( ret > 30 )
			{
				monitor.setDataSum(sum);
				monitor.setCurDate();
				monitor.send(gConf.monitorName);
				time(&monLastT);
				sum=0;
			}
			continue; // no data
		}
		 
		//logPrintS(1,"[STP] -3- []");
		
		// write log
		//로그 주석 처리.2011.11.23.
#ifdef _DEBUG
	    _monPrint(_MONILOG,(char*)hRemoteSock.getMsg());
	    printf("\n\n%s\n\n", (char*)hRemoteSock.getMsg());
#endif
			
#ifdef TIME
		/* gettimeofday */
		struct timeval timefirst, timesecond;
		struct timezone tzp;
		int secBuf, microsecBuf;
		float timeBuf;
		
		gettimeofday(&timefirst,&tzp);
		/* gettimeofday */
#endif
		//logPrintS(1,"nCurAccCnt(%d)",nCurAccCnt);
		
		ret = classifyS(monitor, processInfo, logonDbInfo, db, hRemoteSock);
		
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] classifyS Error ret [%d]",ret);
			goto SenderEND;
		}
		//logPrintS(1,"[STP] -4- []");
			
#ifdef TIME
		gettimeofday(&timesecond,&tzp);
		secBuf 		= (timesecond.tv_sec - timefirst.tv_sec);
		microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
		timeBuf 	= microsecBuf;
		timeBuf 	= timeBuf / 1000000;
		timeBuf 	= timeBuf + secBuf;
		logPrintS(0,"senderProcess db time [%f]",timeBuf);
#endif
    }

SenderEND:
	logPrintS(0,"[INF] socket END sockfd[%d]CID[%s]", hRemoteSock.getSockfd(), logonDbInfo.szCID);
	hRemoteSock.close();
	
	return;
}


int SenderProcess::classifyS(CMonitor& monitor,CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo,CKSSocket& db,CKSSocket& hRemoteSock) 
{
	int ret = 0;
	int rptRet = 0;
	char szYYYYMM[32];
	string strPacketHeader;
	
	CSenderDbMMSID senderDbMMSID;
	CBcastData bCastData;

	strPacketHeader = "";
	strPacketHeader.reserve(0);
			
	strPacketHeader.insert(0,hRemoteSock.getMsg(),30);
	
    cout<<"getMsg"<<hRemoteSock.getMsg()<<"\n"<<endl;
  
	if( strstr(strPacketHeader.c_str(),"BEGIN PING\r\n") ) 
	{
		fflush(stdout);
		//logPrintS(0,"[INF] socket link recv");
		
		ret = sendPong(hRemoteSock);
		monitor.setLinkTime();
		if( ret < 0 ) 
		{
			return ret;
		}
	} 
	else if ( strstr(strPacketHeader.c_str(),"BEGIN MMSSEND\r\n") )  
	{
		/*
		    20140212 : mmsPacketSend.parse fix return 100
			return parse result
				-1 : MData Header info error
				 0 : OK
		*/


#if (DEBUG >= 5)
	logPrintS(1,"[DBG] parse [%.1900s]", hRemoteSock.getMsg());
#endif
		/*
		 * STEP1 : 전문 파싱
		 */
		ret = mmsPacketSend.parse((char*)hRemoteSock.getMsg());
		if( ret != 100 ) 
		{
			logPrintS(0,"[ERR] packet parse failed - ErrMsg[%s]",mmsPacketSend.getErrorMsg());
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"Msg Parsing Error");
			//return ret;
			return 0;
		}

		/*
		 * STEP2 : MMS ID 조회
		 */
		ret = getMMSID2DB(senderDbMMSID, szSenderID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get MMSID failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET MMS ID FAILED.");
			
			rptRet = setMMSRPTTBL(0,0,mmsPacketSend,8003,"GetMMSIDFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] getMMSID2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			}
			
			//return ret;
			return 0;
		}
		
		/*
		 * STEP3 : CTN ID 조회
		 */
		ret = getCTNID2DB(senderDbMMSID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get CTNID failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET CTN ID FAILED.");
			//return ret;
			return 0;
		}	
		
		/*
		 * STEP4 : 전문 로그
		 */
		writeLogMMSData(mmsPacketSend,senderDbMMSID.mmsid,senderDbMMSID.ctnid);// LOG write

		/*
		 * STEP5 : 메시지 파일 생성
		 */
/*
 * 파일생성 안함
 
		CMMSFileProcess mmsFileProcess;// write file object
 	
		memset(szYYYYMM	,0x00	,sizeof(szYYYYMM));  //CCL(szYYYYMM);
		get_timestring("%04d%02d",time(NULL),szYYYYMM);
		trim(szYYYYMM,strlen(szYYYYMM));

		ret = mmsFileProcess.write(db
              					,mmsPacketSend
              					,gConf.ContentPath
              					,senderDbMMSID.mmsid
              					,szYYYYMM
              					,szSenderID
              					,senderDbMMSID.ctnid
              					,gConf.dbRequestTimeOut
              					,senderDbDomainName
              					);

		if(ret < 0 )
		{
			logPrintS(0,"[ERR] file write failed [%d]", ret);

			// ret == -2 : IMG 사이즈 1M 초과 오류
			if ( ret == -2)
				sendAck(hRemoteSock,mmsPacketSend,2104,senderDbMMSID.ctnid ,"컨텐츠 크기 초과");
			else
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"FILE WRITE FAILED.");
			//return ret;
			return 0;
		}
 */
		/*
		 * STEP6 : SEND TABLE 등록
		 */
		if(strncmp(gSenderInfo.szBlockYN,"Y",1)==0)
    	{
    		ret =  sendAck(hRemoteSock,mmsPacketSend,8012, senderDbMMSID.ctnid ,"Fail");
		
			if ( ret  < 0 )
			{
				logPrintS(0,"[ERR] socket send ack failed");
			}

			rptRet = setMMSRPTTBL( 0, senderDbMMSID.mmsid, mmsPacketSend, 8012, "SendBlocked.", szSenderID);
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSRPTTBL block cid[%s] ptn_sn[%s] mmsid[%lld]", 
					szSenderID, 
					mmsPacketSend.getKeyValue(), 
					senderDbMMSID.mmsid
					);
			}
			return 0;
		}
		else
		{			
			ret = setMMSTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT);  // send table
			if ( ret < 0 )
			{
				logPrintS(0,"[ERR] db insert table MMSTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT MMSTBL FAILED.");
				
				rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,8004,"InsertMMSTBLFailed.",szSenderID);
				
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] setMMSTBL2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
				
				return 0;
			}
		}
	
		/*
		 * STEP7 : AQUEUE 전송
		 */
		ret = setMMSMSG2DB_ATK(senderDbMMSID.mmsid, mmsPacketSend, logonDbInfo.nmPRT);
		
		if ( ret < 0 )
		{
			logPrintS(0,"[ERR] db insert MMS MSG failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT MMS MSG FAILED.");
			
			rptRet = setMMSRPTTBL(1,senderDbMMSID.mmsid,mmsPacketSend,8005,"InsertMMSMSGFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSMSG2DB_ATK setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			}
			
			return 0;
		}
				
		/*
		 * STEP8 : CLIENT ACK 메시지 전송
		 */
		ret =  sendAck(hRemoteSock,mmsPacketSend,ret, senderDbMMSID.ctnid ,"Succ");
		if ( ret  < 0 )
		{
			logPrintS(0,"[ERR] socket send ack failed");
		}
		
		monitor.setDataTime();    
		nCurAccCnt++;
        
		/*
		 * STEP9 : 전송건수 제한 체크
		 */
		ret =	SenderLimit(logonDbInfo);
		
		if(ret == -1)
		{
			logPrintS(0,"[ERR] limit SendreLimit ret [%d]",ret);
			return -1;
		}		//	logPrintS(1,"ret(%d)nCurAccCnt(%d)",ret,nCurAccCnt);

	} 
	else  // error
	{
		logPrintS(0,"[INF] invalid msg header data - [%s] ", strPacketHeader.c_str());

		fflush(stdout);
		ret = -1;
	}

	return ret;
}								


int SenderProcess::SenderLimit(CLogonDbInfo& logonDbInfo)
{
	int ret;
	char szTemp[256];
	
	//* < brief 발송 제한 체크
	if (atoi(logonDbInfo.szLimitType) != 0)
	{
		memset(szTemp	,0x00	,sizeof(szTemp));
	
		get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitCurTime);	// 현재 날짜 구하기
		
		if (strcmp(szLimitTime,"") == 0)
		{
			strcpy(szLimitTime,szLimitCurTime);	// 년월일 값 구하기
		}

		ret = LimitCheck(logonDbInfo);
		//카운트 계산에 따른 서비스 제한 및 알림 기능 수행
		// logPrintS(1,"ret(%d)",ret);
		switch (ret)
		{
			case 9 : // 일 변경에 따른 누적 카운트 초기화 및 프로시저 실행
			case 10 : // 월 변경에 따른 누적 카운트 초기화 및 프로시저 실행
				if (ret == 9)
					logPrintS(1,"[INF]day change total count reset and run");
				if (ret == 10)
					logPrintS(1,"[INF]month change total count reset and run");
	
				//발송 제한 변수 초기화
				bDayWarnCheck = false;
				bMonWarnCheck = false;
				
				if (ret == 9)
					logonDbInfo.nMonAccCnt += logonDbInfo.nCurAccCnt;	//일 변경 시 월 카운트는 누적
				if (ret == 10)
					logonDbInfo.nMonAccCnt = 0;	//월 변경시 월 카운트는 초기화
					
				logonDbInfo.nDayAccCnt = 0;	//일,월 변경시 일 카운트는 항상 초기화
				nCurAccCnt = 0;	//일,월 변경시 현재 누적 카운트는 항상 초기화
				memset(szLimitTime,(char)NULL,sizeof(char)*16);
				break;
			default :
				break;
		}
				
		switch (ret)
		{
			case 0 : // 변경 없음
				break;
			case 1 : // 일 서비스 제한
				logPrintS(1,"[INF] daily limit [%d]"	,logonDbInfo.nDayLimitCnt);
				return -1;
			case 2 : // 월 서비스 제한
				logPrintS(1,"[INF] monthly limit [%d]"	,logonDbInfo.nMonLimitCnt);
				return -1;
			case 3 : // 일 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] daily limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(0,"[%s]"	,szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				return -1;
			case 4 : // 월 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] monthly limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(0,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				return -1;
			case 5 : // 일 알람
				sprintf(szTemp,"[INF] daily limit orver - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(1,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				break;
			case 6 : // 월 알람
				sprintf(szTemp,"[INF] monthly limit over - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(1,"%s",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				break;
			case 7 : // 일 임계치 알람
				if (!bDayWarnCheck)
				{
					bDayWarnCheck = true;
					sprintf(szTemp,"[INF] daily limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nDayWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin daily limit msg send failed",0,0);
					}
						
				}
				break;
			case 8 : // 월 임계치 알람
				if (!bMonWarnCheck)
				{
					bMonWarnCheck = true;
					sprintf(szTemp,"[INF] monthly limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nMonWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin monthly limit msg send failed",0,0);
					}
						
				}
				break;
			default :
				break;
		}
	}
	
	return 0;
}


//* < brief 발송 제한 체크
int SenderProcess::LimitCheck(CLogonDbInfo& logonDbInfo)
{
/* return value
	1 : 일 서비스 제한
	2 : 월 서비스 제한
	3 : 일 서비스 제한	+ 알람
	4 : 월 서비스 제한	+ 알람
	5 : 일 알람
	6 : 월 알람
	7 : 일 임계치 알람
	8 : 월 임계치 알람
	9 : 일 변경에 따른 누적 카운트 초기화
	10 : 월 변경에 따른 누적 카운트 초기화
	0 : 변경 없음
*/
	bool bDay=false;
	bool bMon=false;
	int  nDayAccCnt 		= logonDbInfo.nDayAccCnt;
	int  nMonAccCnt 		= logonDbInfo.nMonAccCnt;
	int  nDayWarnCnt 		= logonDbInfo.nDayWarnCnt;
	int  nMonWarnCnt 		= logonDbInfo.nMonWarnCnt;
	int  nDayLimitCnt 		= logonDbInfo.nDayLimitCnt;
	int  nMonLimitCnt 		= logonDbInfo.nMonLimitCnt;
	int  nLimitType 		= atoi(logonDbInfo.szLimitType);
	int  nLimitFlag 		= atoi(logonDbInfo.szLimitFlag);

	//logPrintS(1,"szLimitCurTime(%s)szLimitTime(%s)",szLimitCurTime,szLimitTime);
	if (strncmp(szLimitTime	,szLimitCurTime	,8) != 0)
		bDay = true;	//일 단위
		
	if (strncmp(szLimitTime,szLimitCurTime,6) != 0) 
		bMon = true;	//월 단위

	if (bDay)
	{
		if (bMon)
		{
			return 10;	// 월 변경이 이루어 졌을 경우
		}
		else
		{
			return 9;	// 일 변경이 이루어 졌을 경우
		}
	}
 //logPrintS(1,"nLimitType(%d),nDayWarnCnt(%d),nCurAccCnt(%d),nDayAccCnt(%d)",nLimitType, nDayWarnCnt, nCurAccCnt,nDayAccCnt);
	//서비스 제한 체크
	switch (nLimitType)
	{
		case 0:	//발송 제한 적용 안함
			return 0;
		case 1:	//일,월 발송 제한
		case 2:	//일 발송 제한
		case 3:	// 월 발송 제한
			if (nLimitType == 1 || nLimitType == 2)
			{
				//일 임계치 체크 (임계치와 제한 건수 사이)
				if (((nDayAccCnt + nCurAccCnt) > nDayWarnCnt) && ((nDayAccCnt + nCurAccCnt) < nDayLimitCnt))
				{
					logPrintS(1,"[INF] daily limit - limit over [%d/%d]", nDayWarnCnt, (nDayAccCnt + nCurAccCnt)-nDayWarnCnt);
					return 7;
				}
				//일 서비스 제한 체크
				if ((nDayAccCnt + nCurAccCnt) > nDayLimitCnt)
				{
					logPrintS(1,"[INF] daily limit - config value [%d]", nDayLimitCnt);
				
					switch (nLimitFlag)
					{
						case 1 :
							return 1;
						case 2 :
							return 3;
						case 3 :
							return 5;
						default :
							return 0;
					}
				}
			}
//logPrintS(1,"nMonWarnCnt(%d),nMonAccCnt(%d),nMonLimitCnt(%d)",nMonWarnCnt, nMonAccCnt+nCurAccCnt+nDayAccCnt, nMonLimitCnt);

			if (nLimitType == 1 || nLimitType == 3)
			{
				//월 임계치 체크 (임계치와 제한 건수 사이)
				//20180829 월 임계치 체크 변경 nDayAccCnt 추가
				//if (((nMonAccCnt + nCurAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt) < nMonLimitCnt))
				if (((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt + nDayAccCnt) < nMonLimitCnt))
				{
					logPrintS(1,"[INF] monthly limit - limit over [%d/%d]", nMonWarnCnt, (nMonAccCnt + nCurAccCnt + nDayAccCnt)-nMonWarnCnt);
					return 8;
				}
				//월 서비스 제한 체크
				//	if ((nMonAccCnt + nCurAccCnt) > nMonLimitCnt)
				if ((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonLimitCnt)
				{
					logPrintS(1,"[INF] monthly limit - config value [%d]", nMonLimitCnt);
					switch (nLimitFlag)
					{
						case 1 :
							return 2;
						case 2 :
							return 4;
						case 3 :
							return 6;
						default :
							return 0;
					}
				}
			}
		default:
			return 0;
		break;
	}
	return 0;
}

int setMMSRPTTBL(int type, long long nMMSId, CMMSPacketSend& mmsPacketSend, int nResCode,char* res_text,char* cid)
{
	int ret;
	CSenderDbMMSRPTQUE senderDbMMSRPTQUE;
	/*CMData mData;
	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}*/

	memset(&senderDbMMSRPTQUE,0x00,sizeof(senderDbMMSRPTQUE));

	/*if(strncmp(mData.strEncoding.c_str(), " aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0)
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		//logPrintS(1,"receiverNum:%d", atoi(mmsPacketSend.getPhoneSizeValue()));
		en.decrypt(receiverNum, receiverNum, atoi(mmsPacketSend.getPhoneSizeValue()));
		strcpy(senderDbMMSRPTQUE.szDstAddr, (char*)receiverNum);
		free(receiverNum);
	}
	else
	{
		strcpy(senderDbMMSRPTQUE.szDstAddr , mmsPacketSend.getReceiverValue());
	}*/
	
	strcpy(senderDbMMSRPTQUE.szDstAddr , "010");
	
	strcpy(senderDbMMSRPTQUE.szPtnSn   , mmsPacketSend.getKeyValue()     );
	senderDbMMSRPTQUE.nMMSId 		= nMMSId;
	senderDbMMSRPTQUE.res_code     = nResCode;
	strcpy(senderDbMMSRPTQUE.res_text   , res_text);
	senderDbMMSRPTQUE.nTelcoId = getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, gSenderInfo.szQType);
	senderDbMMSRPTQUE.nType 		= type;
	memcpy(senderDbMMSRPTQUE.szCid, cid, 10);
	
	
	ret = g_oracle.setSendReportData(senderDbMMSRPTQUE);
	
	if (ret < 0)
	{
		if(type == 1)
		{
			logPrintS(0,"[ERR] setSendReportData TBL MMSID[%lld] PtnSn[%s] ret[%d]",nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);
		}else
		{
			logPrintS(0,"[ERR] setSendReportData QUEUE MMSID[%lld] PtnSn[%s] ret[%d]",nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);	
		}
	}
	
	return ret;
}
